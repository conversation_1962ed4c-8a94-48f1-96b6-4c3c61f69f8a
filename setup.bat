@echo off
chcp 65001 >nul
echo جاري فحص متطلبات برنامج إدارة مخزون المطحنة...
echo =============================================
echo.

REM البحث عن بايثون في مسارات مختلفة
set "PYTHON_CMD="
set "PIP_CMD="

REM فحص python
python --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set "PYTHON_CMD=python"
    echo تم العثور على بايثون: python
    goto :check_pip
)

REM فحص python3
python3 --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set "PYTHON_CMD=python3"
    echo تم العثور على بايثون: python3
    goto :check_pip
)

REM فحص في Program Files
if exist "C:\Program Files\Python*\python.exe" (
    for /d %%i in ("C:\Program Files\Python*") do (
        "%%i\python.exe" --version >nul 2>&1
        if !ERRORLEVEL! EQU 0 (
            set "PYTHON_CMD=%%i\python.exe"
            echo تم العثور على بايثون: %%i\python.exe
            goto :check_pip
        )
    )
)

REM فحص في AppData
if exist "%LOCALAPPDATA%\Programs\Python\Python*\python.exe" (
    for /d %%i in ("%LOCALAPPDATA%\Programs\Python\Python*") do (
        "%%i\python.exe" --version >nul 2>&1
        if !ERRORLEVEL! EQU 0 (
            set "PYTHON_CMD=%%i\python.exe"
            echo تم العثور على بايثون: %%i\python.exe
            goto :check_pip
        )
    )
)

echo ❌ خطأ: لم يتم العثور على بايثون صالح للاستخدام!
echo.
echo يرجى تثبيت بايثون 3.8 أو إصدار أحدث من:
echo https://www.python.org/downloads/
echo.
echo تأكد من تحديد خيار "Add Python to PATH" أثناء التثبيت
echo.
pause
exit /b 1

:check_pip
REM البحث عن pip
pip --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set "PIP_CMD=pip"
    echo تم العثور على pip: pip
    goto :install_requirements
)

%PYTHON_CMD% -m pip --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set "PIP_CMD=%PYTHON_CMD% -m pip"
    echo تم العثور على pip: %PYTHON_CMD% -m pip
    goto :install_requirements
)

echo ❌ خطأ: لم يتم العثور على pip!
echo يرجى تشغيل الأمر التالي لتثبيت pip:
echo %PYTHON_CMD% -m ensurepip --upgrade
pause
exit /b 1

:install_requirements
echo.
echo جاري تثبيت المكتبات المطلوبة...
echo ================================
%PIP_CMD% install -r requirements.txt

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم تثبيت جميع المتطلبات بنجاح!
) else (
    echo.
    echo ❌ حدث خطأ أثناء تثبيت المكتبات!
    echo يرجى التحقق من اتصال الإنترنت ومحاولة تشغيل الأمر التالي يدوياً:
    echo %PIP_CMD% install -r requirements.txt
    pause
    exit /b 1
)

echo.
echo يمكنك الآن تشغيل البرنامج عن طريق تشغيل ملف main.py
echo أو استخدام الأمر: %PYTHON_CMD% main.py
echo.
pause

echo جاري تشغيل البرنامج...
%PYTHON_CMD% main.py
