
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import sys
from PIL import Image, ImageTk
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import arabic_reshaper
from bidi.algorithm import get_display
import time
from datetime import datetime

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, RawMaterialsManager

class RawMaterialsScreen(ttb.Frame):
    def __init__(self, parent, db_manager, materials_manager=None):
        super().__init__(parent)

        self.settings = config.load_settings()

        # إنشاء مديري قاعدة البيانات
        self.db_manager = db_manager
        self.materials_manager = materials_manager or RawMaterialsManager(db_manager)

        # تخزين المادة المحددة حالياً
        self.selected_material_id = None

        # إنشاء واجهة المستخدم
        self.create_widgets()

        # تحديث قائمة المواد الخام
        self.update_materials_list()

    def create_widgets(self):
        # العنوان الرئيسي
        header_frame = ttb.Frame(self)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 20))

        title = ui.RTLLabel(
            header_frame, 
            text="إدارة المواد الخام", 
            font=("Tajawal", 24, "bold"),
            bootstyle="primary"
        )
        title.pack(anchor="e", pady=10)

        # إطار البحث
        search_frame = ttb.Frame(header_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=10)

        search_box = ui.RTLSearchBox(
            search_frame, 
            search_callback=self.search_materials,
            placeholder="بحث عن مادة خام..."
        )
        search_box.pack(fill=tk.X, expand=True)

        # إطار الأزرار
        button_frame = ttb.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT, padx=10)

        # زر إضافة مادة جديدة
        add_btn = ttb.Button(
            button_frame, 
            text=ui.format_arabic_text("إضافة مادة جديدة"),
            command=self.add_material,
            bootstyle="success",
            width=15
        )
        add_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة تقرير
        print_btn = ttb.Button(
            button_frame, 
            text=ui.format_arabic_text("طباعة تقرير"),
            command=self.print_report,
            bootstyle="info",
            width=15
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # تقسيم الشاشة إلى قسمين
        # 1. قائمة المواد الخام
        # 2. نموذج تحرير المادة الخام

        # إطار رئيسي للمحتوى
        content_frame = ttb.Frame(self)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إطار قائمة المواد
        materials_list_frame = ttb.Frame(content_frame)
        materials_list_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # قائمة المواد الخام
        self.create_materials_list(materials_list_frame)

        # إطار تفاصيل المادة
        material_details_frame = ttb.Frame(content_frame)
        material_details_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # نموذج تحرير المادة
        self.create_material_form(material_details_frame)

    def create_materials_list(self, parent):
        # عنوان الجدول
        list_title = ui.RTLLabel(
            parent, 
            text="قائمة المواد الخام", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        list_title.pack(anchor="e", pady=(0, 10))

        # إطار الجدول مع شريط التمرير
        table_frame = ttb.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # عمود الجدول
        columns = ("id", "code", "name", "weight", "quantity", "unit")
        headers = ("م", "الرمز", "الاسم", "الوزن", "الكمية", "الوحدة")

        # إنشاء جدول المواد الخام
        self.materials_table = ui.RTLTreeview(
            table_frame,
            columns=columns,
            headers=headers
        )
        self.materials_table.pack(fill=tk.BOTH, expand=True)

        # عرض الأعمدة بشكل مناسب
        self.materials_table.column("id", width=50)
        self.materials_table.column("code", width=100)
        self.materials_table.column("name", width=200)
        self.materials_table.column("weight", width=100)
        self.materials_table.column("quantity", width=100)
        self.materials_table.column("unit", width=80)

        # ربط حدث النقر على العنصر
        self.materials_table.bind("<<TreeviewSelect>>", self.on_material_selected)

    def create_material_form(self, parent):
        # عنوان النموذج
        form_title = ui.RTLLabel(
            parent, 
            text="بيانات المادة الخام", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        form_title.pack(anchor="e", pady=(0, 20))

        # إطار النموذج
        form_frame = ttb.Frame(parent)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        # حقول النموذج
        # رمز المادة
        code_frame = ttb.Frame(form_frame)
        code_frame.pack(fill=tk.X, pady=5)

        code_label = ui.RTLLabel(code_frame, text="رمز المادة:", width=15)
        code_label.pack(side=tk.RIGHT, padx=5)

        self.code_entry = ttb.Entry(code_frame)
        self.code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # اسم المادة
        name_frame = ttb.Frame(form_frame)
        name_frame.pack(fill=tk.X, pady=5)

        name_label = ui.RTLLabel(name_frame, text="اسم المادة:", width=15)
        name_label.pack(side=tk.RIGHT, padx=5)

        self.name_entry = ttb.Entry(name_frame)
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوزن
        weight_frame = ttb.Frame(form_frame)
        weight_frame.pack(fill=tk.X, pady=5)

        weight_label = ui.RTLLabel(weight_frame, text="الوزن:", width=15)
        weight_label.pack(side=tk.RIGHT, padx=5)

        self.weight_entry = ttb.Entry(weight_frame)
        self.weight_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الكمية
        quantity_frame = ttb.Frame(form_frame)
        quantity_frame.pack(fill=tk.X, pady=5)

        quantity_label = ui.RTLLabel(quantity_frame, text="الكمية:", width=15)
        quantity_label.pack(side=tk.RIGHT, padx=5)

        self.quantity_entry = ttb.Entry(quantity_frame)
        self.quantity_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوحدة
        unit_frame = ttb.Frame(form_frame)
        unit_frame.pack(fill=tk.X, pady=5)

        unit_label = ui.RTLLabel(unit_frame, text="الوحدة:", width=15)
        unit_label.pack(side=tk.RIGHT, padx=5)

        # قائمة الوحدات الشائعة
        self.unit_var = tk.StringVar()
        units = ["كجم", "جرام", "طن", "لتر", "مل"]

        unit_combobox = ttb.Combobox(unit_frame, textvariable=self.unit_var, values=units)
        unit_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوصف
        desc_frame = ttb.Frame(form_frame)
        desc_frame.pack(fill=tk.X, pady=5)

        desc_label = ui.RTLLabel(desc_frame, text="الوصف:", width=15)
        desc_label.pack(side=tk.RIGHT, padx=5, anchor="n")

        self.desc_text = ttb.Text(desc_frame, height=4)
        self.desc_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # إطار أزرار العمليات
        actions_frame = ttb.Frame(parent)
        actions_frame.pack(fill=tk.X, pady=20)

        # زر الحفظ
        save_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("حفظ التغييرات"),
            command=self.save_material,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=tk.RIGHT, padx=5)

        # زر الحذف
        delete_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("حذف المادة"),
            command=self.delete_material,
            bootstyle="danger",
            width=15
        )
        delete_btn.pack(side=tk.RIGHT, padx=5)

        # زر إلغاء
        cancel_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("إلغاء"),
            command=self.clear_form,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=5)

        # تعطيل الحقول في البداية
        self.disable_form()

    def update_materials_list(self):
        # مسح الجدول
        self.materials_table.clear_all()

        # الحصول على جميع المواد الخام
        try:
            materials = self.materials_manager.get_all_materials()
            if materials:
                # إضافة المواد إلى الجدول
                for material in materials:
                    self.materials_table.add_row([
                        material.MaterialID,
                        material.MaterialCode,
                        material.MaterialName,
                        material.Weight,
                        material.Quantity,
                        material.Unit or ""
                    ])
        except Exception as e:
            print(f"خطأ في تحديث قائمة المواد الخام: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء تحديث قائمة المواد الخام: {str(e)}", "error")

    def search_materials(self, search_term):
        # مسح الجدول
        self.materials_table.clear_all()

        if not search_term:
            # إذا كان البحث فارغاً، عرض جميع المواد
            self.update_materials_list()
            return

        # البحث عن المواد المطابقة
        try:
            materials = self.materials_manager.search_materials(search_term)
            if materials:
                # إضافة المواد إلى الجدول
                for material in materials:
                    self.materials_table.add_row([
                        material.MaterialID,
                        material.MaterialCode,
                        material.MaterialName,
                        material.Weight,
                        material.Quantity,
                        material.Unit or ""
                    ])
        except Exception as e:
            print(f"خطأ في البحث عن المواد الخام: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء البحث عن المواد الخام: {str(e)}", "error")

    def on_material_selected(self, event):
        # الحصول على العنصر المحدد
        selected_item = self.materials_table.selection()
        if not selected_item:
            return

        item = self.materials_table.item(selected_item[0])
        material_id = item["values"][0]

        # تحميل بيانات المادة
        try:
            material = self.materials_manager.get_material_by_id(material_id)
            if material:
                self.selected_material_id = material.MaterialID
                self.enable_form()
                self.populate_form(material)
        except Exception as e:
            print(f"خطأ في تحميل بيانات المادة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء تحميل بيانات المادة: {str(e)}", "error")

    def populate_form(self, material):
        # ملء حقول النموذج ببيانات المادة
        self.code_entry.delete(0, tk.END)
        self.code_entry.insert(0, material.MaterialCode)
        self.code_entry.configure(state="disabled")  # لا يمكن تغيير الرمز بعد الإنشاء

        self.name_entry.delete(0, tk.END)
        self.name_entry.insert(0, material.MaterialName)

        self.weight_entry.delete(0, tk.END)
        self.weight_entry.insert(0, str(material.Weight) if material.Weight else "0")

        self.quantity_entry.delete(0, tk.END)
        self.quantity_entry.insert(0, str(material.Quantity) if material.Quantity else "0")

        self.unit_var.set(material.Unit if material.Unit else "")

        self.desc_text.delete("1.0", tk.END)
        if material.Description:
            self.desc_text.insert("1.0", material.Description)

    def enable_form(self):
        # تمكين حقول النموذج
        self.name_entry.configure(state="normal")
        self.weight_entry.configure(state="normal")
        self.quantity_entry.configure(state="normal")
        self.unit_var.set("")
        self.desc_text.configure(state="normal")

    def disable_form(self):
        # تعطيل حقول النموذج
        self.code_entry.configure(state="disabled")
        self.name_entry.configure(state="disabled")
        self.weight_entry.configure(state="disabled")
        self.quantity_entry.configure(state="disabled")
        self.desc_text.configure(state="disabled")

    def clear_form(self):
        # مسح الحقول وإعادة تعطيلها
        self.selected_material_id = None
        self.code_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        self.weight_entry.delete(0, tk.END)
        self.quantity_entry.delete(0, tk.END)
        self.unit_var.set("")
        self.desc_text.delete("1.0", tk.END)
        self.disable_form()

        # إلغاء تحديد العناصر في الجدول
        self.materials_table.selection_remove(self.materials_table.selection())

    def add_material(self):
        # إضافة مادة خام جديدة
        self.selected_material_id = None
        self.enable_form()
        self.code_entry.configure(state="normal")

        # مسح الحقول
        self.clear_form()
        self.code_entry.configure(state="normal")

        # تركيز على حقل الرمز
        self.code_entry.focus_set()

    def save_material(self):
        # حفظ بيانات المادة
        try:
            code = self.code_entry.get().strip()
            name = self.name_entry.get().strip()

            # التحقق من الحقول المطلوبة
            if not code or not name:
                ui.arabic_messagebox("تنبيه", "يجب إدخال رمز المادة واسم المادة", "warning")
                return

            # التحقق من القيم العددية
            try:
                weight = float(self.weight_entry.get().strip() or 0)
                quantity = float(self.quantity_entry.get().strip() or 0)
            except ValueError:
                ui.arabic_messagebox("خطأ", "يجب إدخال قيم صحيحة للوزن والكمية", "error")
                return

            unit = self.unit_var.get().strip()
            description = self.desc_text.get("1.0", tk.END).strip()

            if self.selected_material_id:
                # تحديث مادة موجودة
                success = self.materials_manager.update_material(
                    self.selected_material_id, name, weight, quantity, unit, description
                )
                if success:
                    ui.arabic_messagebox("نجاح", f"تم تحديث المادة الخام: {name}", "info")
                else:
                    ui.arabic_messagebox("خطأ", "فشل تحديث المادة الخام", "error")
            else:
                # إضافة مادة جديدة
                # التحقق من عدم وجود تكرار للرمز
                existing_material = self.materials_manager.get_material_by_code(code)
                if existing_material:
                    ui.arabic_messagebox("تنبيه", f"يوجد بالفعل مادة خام برمز: {code}", "warning")
                    return

                success = self.materials_manager.add_material(
                    code, name, weight, quantity, unit, description
                )
                if success:
                    ui.arabic_messagebox("نجاح", f"تم إضافة المادة الخام: {name}", "info")
                else:
                    ui.arabic_messagebox("خطأ", "فشل إضافة المادة الخام", "error")

            # تحديث القائمة وتنظيف النموذج
            self.update_materials_list()
            self.clear_form()

        except Exception as e:
            print(f"خطأ في حفظ بيانات المادة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء حفظ بيانات المادة: {str(e)}", "error")

    def delete_material(self):
        if not self.selected_material_id:
            ui.arabic_messagebox("تنبيه", "يرجى تحديد مادة خام للحذف", "warning")
            return

        # تأكيد الحذف
        confirm = ui.arabic_messagebox(
            "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذه المادة الخام؟ لا يمكن التراجع عن هذا الإجراء.",
            "yesno"
        )
        if confirm:
            try:
                success = self.materials_manager.delete_material(self.selected_material_id)
                if success:
                    ui.arabic_messagebox("نجاح", "تم حذف المادة الخام بنجاح", "info")
                    # تحديث القائمة وتنظيف النموذج
                    self.update_materials_list()
                    self.clear_form()
                else:
                    ui.arabic_messagebox("خطأ", "لا يمكن حذف المادة الخام لأنها مستخدمة في خلطات", "error")
            except Exception as e:
                print(f"خطأ في حذف المادة: {e}")
                ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء محاولة حذف المادة: {str(e)}", "error")

    def print_report(self):
        # طباعة تقرير بالمواد الخام
        try:
            # الحصول على المواد
            materials = self.materials_manager.get_all_materials()

            if not materials:
                ui.arabic_messagebox("تنبيه", "لا توجد مواد خام لطباعة التقرير", "warning")
                return

            # حوار اختيار مكان حفظ الملف
            file_path = filedialog.asksaveasfilename(
                title="حفظ تقرير المواد الخام",
                defaultextension=".pdf",
                filetypes=[("PDF Files", "*.pdf")]
            )

            if not file_path:
                return  # المستخدم ألغى العملية

            # إنشاء التقرير
            self.create_materials_pdf_report(file_path, materials)

            # فتح الملف بعد الإنشاء
            os.startfile(file_path)

        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}", "error")

    def create_materials_pdf_report(self, file_path, materials):
        # إنشاء ملف PDF للتقرير
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=30,
            leftMargin=30,
            topMargin=30,
            bottomMargin=30
        )

        # تحضير المحتوى
        elements = []

        # أنماط النصوص
        styles = getSampleStyleSheet()

        # عنوان التقرير
        title_text = arabic_reshaper.reshape("تقرير المواد الخام")
        title_text = get_display(title_text)
        elements.append(Paragraph(title_text, styles['Title']))
        elements.append(Spacer(1, 20))

        # تاريخ التقرير
        date_text = arabic_reshaper.reshape(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}")
        date_text = get_display(date_text)
        elements.append(Paragraph(date_text, styles['Normal']))
        elements.append(Spacer(1, 10))

        # بيانات الجدول
        data = [
            [
                get_display(arabic_reshaper.reshape("م")),
                get_display(arabic_reshaper.reshape("الرمز")),
                get_display(arabic_reshaper.reshape("اسم المادة")),
                get_display(arabic_reshaper.reshape("الوزن")),
                get_display(arabic_reshaper.reshape("الكمية")),
                get_display(arabic_reshaper.reshape("الوحدة"))
            ]
        ]

        for i, material in enumerate(materials, 1):
            data.append([
                i,
                material.MaterialCode,
                get_display(arabic_reshaper.reshape(material.MaterialName)),
                material.Weight if material.Weight else 0,
                material.Quantity if material.Quantity else 0,
                get_display(arabic_reshaper.reshape(material.Unit)) if material.Unit else ""
            ])

        # إنشاء الجدول
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))

        elements.append(table)

        # إضافة معلومات التذييل
        elements.append(Spacer(1, 20))
        footer_text = arabic_reshaper.reshape("نظام إدارة مخزون المطحنة © " + str(datetime.now().year))
        footer_text = get_display(footer_text)
        elements.append(Paragraph(footer_text, styles['Normal']))

        # بناء المستند
        doc.build(elements)
