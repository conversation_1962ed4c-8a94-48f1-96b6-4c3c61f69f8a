
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import sys
from PIL import Image, ImageTk
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import arabic_reshaper
from bidi.algorithm import get_display
import time
from datetime import datetime

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, MixturesManager, RawMaterialsManager

class MixturesScreen(ttb.Frame):
    def __init__(self, parent, db_manager, mixtures_manager=None, raw_materials_manager=None):
        super().__init__(parent)

        self.settings = config.load_settings()

        # إنشاء مديري قاعدة البيانات
        self.db_manager = db_manager
        self.mixtures_manager = mixtures_manager or MixturesManager(db_manager)
        self.raw_materials_manager = raw_materials_manager or RawMaterialsManager(db_manager)

        # تخزين الخلطة المحددة حالياً
        self.selected_mixture_id = None

        # تخزين مكونات الخلطة الحالية
        self.current_components = []

        # إنشاء واجهة المستخدم
        self.create_widgets()

        # تحديث قائمة الخلطات
        self.update_mixtures_list()

    def create_widgets(self):
        # العنوان الرئيسي
        header_frame = ttb.Frame(self)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 20))

        title = ui.RTLLabel(
            header_frame, 
            text="إدارة الخلطات والمطاحين", 
            font=("Tajawal", 24, "bold"),
            bootstyle="primary"
        )
        title.pack(anchor="e", pady=10)

        # إطار البحث
        search_frame = ttb.Frame(header_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=10)

        search_box = ui.RTLSearchBox(
            search_frame, 
            search_callback=self.search_mixtures,
            placeholder="بحث عن خلطة أو مطحون..."
        )
        search_box.pack(fill=tk.X, expand=True)

        # إطار الأزرار
        button_frame = ttb.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT, padx=10)

        # زر إضافة خلطة جديدة
        add_btn = ttb.Button(
            button_frame, 
            text=ui.format_arabic_text("إضافة خلطة جديدة"),
            command=self.add_mixture,
            bootstyle="success",
            width=15
        )
        add_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة تقرير
        print_btn = ttb.Button(
            button_frame, 
            text=ui.format_arabic_text("طباعة تقرير"),
            command=self.print_report,
            bootstyle="info",
            width=15
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # إطار المحتوى مقسم إلى جانبين
        content_frame = ttb.Frame(self)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # الجانب الأيمن: قائمة الخلطات
        mixtures_list_frame = ttb.Frame(content_frame)
        mixtures_list_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10))

        # قائمة الخلطات
        self.create_mixtures_list(mixtures_list_frame)

        # الجانب الأيسر: تفاصيل الخلطة ومكوناتها
        details_frame = ttb.Frame(content_frame)
        details_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # بيانات الخلطة والمكونات
        self.create_mixture_details(details_frame)

    def create_mixtures_list(self, parent):
        # عنوان القائمة
        list_title = ui.RTLLabel(
            parent, 
            text="قائمة الخلطات", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        list_title.pack(anchor="e", pady=(0, 10))

        # إطار الجدول مع شريط التمرير
        table_frame = ttb.Frame(parent, width=300)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # عمود الجدول
        columns = ("id", "code", "name", "op_code")
        headers = ("م", "الرمز", "الاسم", "الكود التشغيلي")

        # إنشاء جدول الخلطات
        self.mixtures_table = ui.RTLTreeview(
            table_frame,
            columns=columns,
            headers=headers
        )
        self.mixtures_table.pack(fill=tk.BOTH, expand=True)

        # عرض الأعمدة بشكل مناسب
        self.mixtures_table.column("id", width=40)
        self.mixtures_table.column("code", width=80)
        self.mixtures_table.column("name", width=150)
        self.mixtures_table.column("op_code", width=100)

        # ربط حدث النقر على العنصر
        self.mixtures_table.bind("<<TreeviewSelect>>", self.on_mixture_selected)

    def create_mixture_details(self, parent):
        # إطار لتفاصيل الخلطة
        mixture_frame = ttb.Frame(parent)
        mixture_frame.pack(fill=tk.X, pady=(0, 10))

        # عنوان
        details_title = ui.RTLLabel(
            mixture_frame, 
            text="بيانات الخلطة", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        details_title.pack(anchor="e", pady=(0, 10))

        # إطار النموذج
        form_frame = ttb.Frame(mixture_frame)
        form_frame.pack(fill=tk.X, padx=10)

        # حقول بيانات الخلطة
        # رمز الخلطة
        code_frame = ttb.Frame(form_frame)
        code_frame.pack(fill=tk.X, pady=5)

        code_label = ui.RTLLabel(code_frame, text="رمز الخلطة:", width=15)
        code_label.pack(side=tk.RIGHT, padx=5)

        self.code_entry = ttb.Entry(code_frame)
        self.code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # اسم الخلطة
        name_frame = ttb.Frame(form_frame)
        name_frame.pack(fill=tk.X, pady=5)

        name_label = ui.RTLLabel(name_frame, text="اسم الخلطة:", width=15)
        name_label.pack(side=tk.RIGHT, padx=5)

        self.name_entry = ttb.Entry(name_frame)
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الكود التشغيلي
        op_code_frame = ttb.Frame(form_frame)
        op_code_frame.pack(fill=tk.X, pady=5)

        op_code_label = ui.RTLLabel(op_code_frame, text="الكود التشغيلي:", width=15)
        op_code_label.pack(side=tk.RIGHT, padx=5)

        self.op_code_entry = ttb.Entry(op_code_frame)
        self.op_code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوصف
        desc_frame = ttb.Frame(form_frame)
        desc_frame.pack(fill=tk.X, pady=5)

        desc_label = ui.RTLLabel(desc_frame, text="الوصف:", width=15)
        desc_label.pack(side=tk.RIGHT, padx=5, anchor="n")

        self.desc_text = ttb.Text(desc_frame, height=2)
        self.desc_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # أزرار العمليات على الخلطة
        buttons_frame = ttb.Frame(form_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        save_btn = ttb.Button(
            buttons_frame, 
            text=ui.format_arabic_text("حفظ بيانات الخلطة"),
            command=self.save_mixture,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=tk.RIGHT, padx=5)

        delete_btn = ttb.Button(
            buttons_frame, 
            text=ui.format_arabic_text("حذف الخلطة"),
            command=self.delete_mixture,
            bootstyle="danger",
            width=15
        )
        delete_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = ttb.Button(
            buttons_frame, 
            text=ui.format_arabic_text("إلغاء"),
            command=self.clear_form,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=5)

        # إطار مكونات الخلطة
        components_frame = ttb.Labelframe(parent, text=ui.format_arabic_text("مكونات الخلطة"))
        components_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # إطار إضافة مكون جديد
        add_component_frame = ttb.Frame(components_frame)
        add_component_frame.pack(fill=tk.X, padx=10, pady=10)

        # اختيار المادة الخام
        material_label = ui.RTLLabel(add_component_frame, text="المادة الخام:")
        material_label.pack(side=tk.RIGHT, padx=5)

        # قائمة منسدلة للمواد الخام
        self.material_var = tk.StringVar()
        self.material_combobox = ttb.Combobox(add_component_frame, textvariable=self.material_var, width=30)
        self.material_combobox.pack(side=tk.RIGHT, padx=5)

        # كمية المكون
        quantity_label = ui.RTLLabel(add_component_frame, text="الكمية:")
        quantity_label.pack(side=tk.RIGHT, padx=5)

        self.quantity_entry = ttb.Entry(add_component_frame, width=10)
        self.quantity_entry.pack(side=tk.RIGHT, padx=5)

        # زر إضافة المكون
        add_comp_btn = ttb.Button(
            add_component_frame, 
            text=ui.format_arabic_text("إضافة المكون"),
            command=self.add_component,
            bootstyle="info",
            width=12
        )
        add_comp_btn.pack(side=tk.LEFT, padx=5)

        # قائمة المكونات
        components_list_frame = ttb.Frame(components_frame)
        components_list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # جدول المكونات
        columns = ("id", "material_id", "material_code", "material_name", "quantity", "unit")
        headers = ("م", "رمز المادة", "كود المادة", "اسم المادة", "الكمية", "الوحدة")

        self.components_table = ui.RTLTreeview(
            components_list_frame,
            columns=columns,
            headers=headers
        )
        self.components_table.pack(fill=tk.BOTH, expand=True)

        # عرض الأعمدة بشكل مناسب
        self.components_table.column("id", width=40)
        self.components_table.column("material_id", width=0)  # إخفاء عمود معرف المادة
        self.components_table.column("material_code", width=100)
        self.components_table.column("material_name", width=200)
        self.components_table.column("quantity", width=80)
        self.components_table.column("unit", width=80)

        # زر حذف المكون المحدد
        remove_comp_btn = ttb.Button(
            components_frame, 
            text=ui.format_arabic_text("حذف المكون المحدد"),
            command=self.remove_component,
            bootstyle="danger-outline",
            width=15
        )
        remove_comp_btn.pack(side=tk.LEFT, padx=10, pady=(0, 10))

        # تعطيل النموذج في البداية
        self.disable_form()

        # تحديث قائمة المواد الخام في القائمة المنسدلة
        self.update_materials_combobox()

    def update_materials_combobox(self):
        try:
            # الحصول على جميع المواد الخام
            materials = self.raw_materials_manager.get_all_materials()
            if materials:
                # تنسيق المواد للعرض في القائمة المنسدلة
                material_list = [f"{m.MaterialCode} - {m.MaterialName}" for m in materials]
                self.material_combobox['values'] = material_list
        except Exception as e:
            print(f"خطأ في تحديث قائمة المواد الخام: {e}")

    def update_mixtures_list(self):
        # مسح الجدول
        self.mixtures_table.clear_all()

        # الحصول على جميع الخلطات
        try:
            mixtures = self.mixtures_manager.get_all_mixtures()
            if mixtures:
                # إضافة الخلطات إلى الجدول
                for mixture in mixtures:
                    self.mixtures_table.add_row([
                        mixture.MixtureID,
                        mixture.MixtureCode,
                        mixture.MixtureName,
                        mixture.OperationalCode
                    ])
        except Exception as e:
            print(f"خطأ في تحديث قائمة الخلطات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء تحديث قائمة الخلطات: {str(e)}", "error")

    def search_mixtures(self, search_term):
        # مسح الجدول
        self.mixtures_table.clear_all()

        if not search_term:
            # إذا كان البحث فارغاً، عرض جميع الخلطات
            self.update_mixtures_list()
            return

        # البحث عن الخلطات المطابقة
        try:
            mixtures = self.mixtures_manager.search_mixtures(search_term)
            if mixtures:
                # إضافة الخلطات إلى الجدول
                for mixture in mixtures:
                    self.mixtures_table.add_row([
                        mixture.MixtureID,
                        mixture.MixtureCode,
                        mixture.MixtureName,
                        mixture.OperationalCode
                    ])
        except Exception as e:
            print(f"خطأ في البحث عن الخلطات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء البحث عن الخلطات: {str(e)}", "error")

    def on_mixture_selected(self, event):
        # الحصول على العنصر المحدد
        selected_item = self.mixtures_table.selection()
        if not selected_item:
            return

        item = self.mixtures_table.item(selected_item[0])
        mixture_id = item["values"][0]

        # تحميل بيانات الخلطة
        try:
            mixture = self.mixtures_manager.get_mixture_by_id(mixture_id)
            if mixture:
                self.selected_mixture_id = mixture.MixtureID
                self.enable_form()
                self.populate_form(mixture)
                self.update_components_list(mixture_id)
        except Exception as e:
            print(f"خطأ في تحميل بيانات الخلطة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء تحميل بيانات الخلطة: {str(e)}", "error")

    def populate_form(self, mixture):
        # ملء حقول النموذج ببيانات الخلطة
        self.code_entry.delete(0, tk.END)
        self.code_entry.insert(0, mixture.MixtureCode or "")

        self.name_entry.delete(0, tk.END)
        self.name_entry.insert(0, mixture.MixtureName or "")

        self.op_code_entry.delete(0, tk.END)
        self.op_code_entry.insert(0, mixture.OperationalCode or "")

        self.desc_text.delete('1.0', tk.END)
        self.desc_text.insert('1.0', mixture.Description or "")

    def update_components_list(self, mixture_id):
        # مسح جدول المكونات
        self.components_table.clear_all()

        # الحصول على مكونات الخلطة
        try:
            components = self.mixtures_manager.get_mixture_components(mixture_id)
            self.current_components = components

            if components:
                # إضافة المكونات إلى الجدول
                for i, comp in enumerate(components, 1):
                    self.components_table.add_row([
                        i,
                        comp.MaterialID,
                        comp.MaterialCode,
                        comp.MaterialName,
                        comp.Quantity,
                        comp.Unit or ""
                    ])
        except Exception as e:
            print(f"خطأ في تحديث قائمة مكونات الخلطة: {e}")
            self.current_components = []

    def enable_form(self):
        # تفعيل حقول النموذج
        self.code_entry.configure(state="normal")
        self.name_entry.configure(state="normal")
        self.op_code_entry.configure(state="normal")
        self.desc_text.configure(state="normal")

    def disable_form(self):
        # تعطيل حقول النموذج
        self.clear_form()
        self.code_entry.configure(state="disabled")
        self.name_entry.configure(state="disabled")
        self.op_code_entry.configure(state="disabled")
        self.desc_text.configure(state="disabled")

    def clear_form(self):
        # مسح جميع حقول النموذج
        self.selected_mixture_id = None
        self.code_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        self.op_code_entry.delete(0, tk.END)
        self.desc_text.delete('1.0', tk.END)
        self.components_table.clear_all()
        self.current_components = []

    def add_mixture(self):
        # تفعيل النموذج لإضافة خلطة جديدة
        self.selected_mixture_id = None
        self.enable_form()
        self.clear_form()
        self.code_entry.focus()

    def save_mixture(self):
        # التحقق من البيانات المدخلة
        code = self.code_entry.get().strip()
        name = self.name_entry.get().strip()
        op_code = self.op_code_entry.get().strip()
        description = self.desc_text.get('1.0', tk.END).strip()

        if not code or not name or not op_code:
            ui.arabic_messagebox("تنبيه", "الرجاء إدخال جميع البيانات المطلوبة: الرمز، الاسم، والكود التشغيلي", "warning")
            return

        try:
            if self.selected_mixture_id:
                # تحديث خلطة موجودة
                success = self.mixtures_manager.update_mixture(
                    self.selected_mixture_id, 
                    name, 
                    op_code, 
                    description
                )

                if success:
                    ui.arabic_messagebox("نجاح", "تم تحديث بيانات الخلطة بنجاح", "info")
                    self.update_mixtures_list()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في تحديث بيانات الخلطة", "error")
            else:
                # إضافة خلطة جديدة
                mixture_id = self.mixtures_manager.add_mixture(
                    code, 
                    name, 
                    op_code, 
                    description
                )

                if mixture_id:
                    self.selected_mixture_id = mixture_id
                    ui.arabic_messagebox("نجاح", "تم إضافة الخلطة بنجاح", "info")
                    self.update_mixtures_list()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في إضافة الخلطة الجديدة", "error")
        except Exception as e:
            print(f"خطأ في حفظ بيانات الخلطة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء حفظ بيانات الخلطة: {str(e)}", "error")

    def delete_mixture(self):
        if not self.selected_mixture_id:
            ui.arabic_messagebox("تنبيه", "الرجاء اختيار خلطة للحذف", "warning")
            return

        # تأكيد الحذف
        confirm = ui.arabic_messagebox(
            "تأكيد الحذف", 
            "هل أنت متأكد من رغبتك في حذف هذه الخلطة؟ سيتم حذف جميع مكوناتها أيضاً.",
            "yesno"
        )

        if confirm:
            try:
                success = self.mixtures_manager.delete_mixture(self.selected_mixture_id)
                if success:
                    ui.arabic_messagebox("نجاح", "تم حذف الخلطة بنجاح", "info")
                    self.clear_form()
                    self.disable_form()
                    self.update_mixtures_list()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في حذف الخلطة", "error")
            except Exception as e:
                print(f"خطأ في حذف الخلطة: {e}")
                ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء حذف الخلطة: {str(e)}", "error")

    def add_component(self):
        if not self.selected_mixture_id:
            ui.arabic_messagebox("تنبيه", "الرجاء حفظ بيانات الخلطة أولاً", "warning")
            return

        # الحصول على المادة المحددة والكمية
        material_str = self.material_var.get().strip()
        quantity_str = self.quantity_entry.get().strip()

        if not material_str or not quantity_str:
            ui.arabic_messagebox("تنبيه", "الرجاء اختيار مادة وإدخال الكمية", "warning")
            return

        try:
            # استخراج رمز المادة من النص المحدد
            material_code = material_str.split(' - ')[0].strip()

            # التحقق من صحة الكمية
            try:
                quantity = float(quantity_str)
                if quantity <= 0:
                    raise ValueError("الكمية يجب أن تكون أكبر من صفر")
            except ValueError:
                ui.arabic_messagebox("خطأ", "الرجاء إدخال كمية صحيحة", "error")
                return

            # الحصول على معلومات المادة
            material = self.raw_materials_manager.get_material_by_code(material_code)
            if not material:
                ui.arabic_messagebox("خطأ", "المادة المحددة غير موجودة", "error")
                return

            # إضافة المكون إلى الخلطة
            success = self.mixtures_manager.add_mixture_component(
                self.selected_mixture_id, 
                material.MaterialID, 
                quantity
            )

            if success:
                # مسح حقول الإدخال
                self.material_var.set("")
                self.quantity_entry.delete(0, tk.END)

                # تحديث قائمة المكونات
                self.update_components_list(self.selected_mixture_id)
            else:
                ui.arabic_messagebox("خطأ", "فشل في إضافة المكون إلى الخلطة", "error")
        except Exception as e:
            print(f"خطأ في إضافة مكون للخلطة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء إضافة المكون للخلطة: {str(e)}", "error")

    def remove_component(self):
        if not self.selected_mixture_id:
            return

        # الحصول على المكون المحدد
        selected_item = self.components_table.selection()
        if not selected_item:
            ui.arabic_messagebox("تنبيه", "الرجاء اختيار مكون للحذف", "warning")
            return

        # تأكيد الحذف
        confirm = ui.arabic_messagebox("تأكيد", "هل أنت متأكد من حذف هذا المكون من الخلطة؟", "yesno")
        if not confirm:
            return

        # الحصول على معرف المكون
        item = self.components_table.item(selected_item[0])
        component_index = int(item["values"][0]) - 1

        if component_index < 0 or component_index >= len(self.current_components):
            return

        component = self.current_components[component_index]
        component_id = component.ComponentID

        try:
            # حذف المكون
            success = self.mixtures_manager.delete_mixture_component(component_id)

            if success:
                ui.arabic_messagebox("نجاح", "تم حذف المكون من الخلطة بنجاح", "info")
                self.update_components_list(self.selected_mixture_id)
            else:
                ui.arabic_messagebox("خطأ", "فشل في حذف المكون من الخلطة", "error")
        except Exception as e:
            print(f"خطأ في حذف مكون من الخلطة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء حذف المكون من الخلطة: {str(e)}", "error")

    def print_report(self):
        # إنشاء تقرير للخلطات
        if self.selected_mixture_id:
            # طباعة تقرير للخلطة المحددة
            self.generate_mixture_report(self.selected_mixture_id)
        else:
            # طباعة تقرير لجميع الخلطات
            self.generate_mixtures_report()

    def generate_mixture_report(self, mixture_id):
        try:
            # الحصول على بيانات الخلطة
            mixture = self.mixtures_manager.get_mixture_by_id(mixture_id)
            if not mixture:
                ui.arabic_messagebox("خطأ", "لم يتم العثور على الخلطة المحددة", "error")
                return

            # الحصول على مكونات الخلطة
            components = self.mixtures_manager.get_mixture_components(mixture_id)

            # إنشاء اسم الملف
            file_name = f"تقرير_خلطة_{mixture.MixtureCode}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ تقرير الخلطة",
                initialfile=file_name
            )

            if not file_path:
                return

            # إنشاء التقرير
            # يمكن تنفيذ إنشاء التقرير بالتفصيل هنا
            # ...

            ui.arabic_messagebox("نجاح", f"تم إنشاء التقرير بنجاح وحفظه في: {file_path}", "info")
        except Exception as e:
            print(f"خطأ في إنشاء تقرير الخلطة: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء إنشاء تقرير الخلطة: {str(e)}", "error")

    def generate_mixtures_report(self):
        try:
            # إنشاء اسم الملف
            file_name = f"تقرير_الخلطات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ تقرير الخلطات",
                initialfile=file_name
            )

            if not file_path:
                return

            # إنشاء التقرير
            # يمكن تنفيذ إنشاء التقرير بالتفصيل هنا
            # ...

            ui.arabic_messagebox("نجاح", f"تم إنشاء التقرير بنجاح وحفظه في: {file_path}", "info")
        except Exception as e:
            print(f"خطأ في إنشاء تقرير الخلطات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء إنشاء تقرير الخلطات: {str(e)}", "error")
