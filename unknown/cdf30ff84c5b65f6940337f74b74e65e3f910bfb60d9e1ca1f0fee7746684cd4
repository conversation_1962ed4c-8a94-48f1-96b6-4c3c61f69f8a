
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import arabic_reshaper
from bidi.algorithm import get_display
from PIL import Image, ImageTk
import config

# وظيفة لتحويل النص إلى صيغة مناسبة للعربية
def format_arabic_text(text):
    reshaped_text = arabic_reshaper.reshape(text)
    bidi_text = get_display(reshaped_text)
    return bidi_text

# فئة القاعدة لجميع الإطارات
class RTLFrame(ttb.Frame):
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.settings = config.load_settings()
        # تعيين اتجاه القراءة من اليمين إلى اليسار
        if self.settings.get("rtl", True):
            self.tk_setPalette(background=self.settings["theme"]["bg_color"])

# فئة القاعدة لجميع النوافذ
class RTLWindow(ttb.Toplevel):
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        self.settings = config.load_settings()
        self.title(title)
        self.geometry("800x600")
        self.configure(background=self.settings["theme"]["bg_color"])

        # تعيين اتجاه القراءة من اليمين إلى اليسار
        if self.settings.get("rtl", True):
            self.tk_setPalette(background=self.settings["theme"]["bg_color"])

        # تمركز النافذة على الشاشة
        self.center_window()

    def center_window(self):
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

# عنصر تحكم للعنوان بالعربية
class RTLLabel(ttb.Label):
    def __init__(self, parent, text="", **kwargs):
        # تهيئة النص للعرض بالعربية
        if text:
            displayed_text = format_arabic_text(text)
        else:
            displayed_text = ""
        super().__init__(parent, text=displayed_text, **kwargs)

        # تخزين النص الأصلي قبل التنسيق
        self.original_text = text

    def set_text(self, text):
        self.original_text = text
        displayed_text = format_arabic_text(text)
        self.config(text=displayed_text)

# عنصر تحكم للأزرار بالعربية
class RTLButton(ttb.Button):
    def __init__(self, parent, text="", command=None, **kwargs):
        # تهيئة النص للعرض بالعربية
        if text:
            displayed_text = format_arabic_text(text)
        else:
            displayed_text = ""
        super().__init__(parent, text=displayed_text, command=command, **kwargs)

        # تخزين النص الأصلي قبل التنسيق
        self.original_text = text

# عنصر تحكم لإدخال النص
class RTLEntry(ttb.Entry):
    def __init__(self, parent, placeholder="", **kwargs):
        super().__init__(parent, **kwargs)

        self.placeholder = placeholder
        self.placeholder_color = 'grey'
        self.default_fg_color = self['foreground']

        if placeholder:
            # وضع نص التلميح
            self.insert(0, format_arabic_text(placeholder))
            self['foreground'] = self.placeholder_color

        # ربط الأحداث
        self.bind("<FocusIn>", self._on_focus_in)
        self.bind("<FocusOut>", self._on_focus_out)

    def _on_focus_in(self, event):
        if self['foreground'] == self.placeholder_color:
            self.delete(0, tk.END)
            self['foreground'] = self.default_fg_color

    def _on_focus_out(self, event):
        if not self.get():
            self.insert(0, format_arabic_text(self.placeholder))
            self['foreground'] = self.placeholder_color

    def get_value(self):
        # الحصول على القيمة مع تجاهل النص التلميحي
        if self['foreground'] == self.placeholder_color:
            return ""
        return self.get()

# فئة لعنصر الجدول مع دعم العربية
class RTLTreeview(ttk.Treeview):
    def __init__(self, parent, columns, headers, **kwargs):
        self.settings = config.load_settings()

        # تجهيز الأعمدة
        super().__init__(parent, columns=columns, show='headings', **kwargs)

        # تعيين عناوين الأعمدة
        for i, col in enumerate(columns):
            self.heading(col, text=format_arabic_text(headers[i]), anchor="center")
            self.column(col, anchor="center", minwidth=100, width=100)

        # إضافة شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.yview)
        self.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def add_row(self, values):
        # تنسيق القيم العربية
        formatted_values = [format_arabic_text(str(val)) if isinstance(val, str) else val for val in values]
        self.insert('', tk.END, values=formatted_values)

    def clear_all(self):
        for item in self.get_children():
            self.delete(item)

# عنصر للبحث مع دعم العربية
class RTLSearchBox(ttb.Frame):
    def __init__(self, parent, search_callback=None, placeholder="بحث...", **kwargs):
        super().__init__(parent, **kwargs)
        self.settings = config.load_settings()

        # تخطيط عناصر البحث
        self.search_var = tk.StringVar()

        # صندوق البحث
        self.search_entry = RTLEntry(self, placeholder=placeholder)
        self.search_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5)

        # زر البحث
        self.search_button = RTLButton(
            self, 
            text="بحث", 
            command=lambda: search_callback(self.search_entry.get_value()) if search_callback else None,
            bootstyle=PRIMARY
        )
        self.search_button.pack(side=tk.LEFT, padx=5)

        # ربط مفتاح الإدخال للبحث
        self.search_entry.bind("<Return>", 
            lambda event: search_callback(self.search_entry.get_value()) if search_callback else None)

# صندوق رسائل عربي
def arabic_messagebox(title, message, type="info"):
    title_formatted = format_arabic_text(title)
    message_formatted = format_arabic_text(message)

    if type == "info":
        return messagebox.showinfo(title_formatted, message_formatted)
    elif type == "error":
        return messagebox.showerror(title_formatted, message_formatted)
    elif type == "warning":
        return messagebox.showwarning(title_formatted, message_formatted)
    elif type == "question":
        return messagebox.askquestion(title_formatted, message_formatted)
    elif type == "yesno":
        return messagebox.askyesno(title_formatted, message_formatted)
    elif type == "okcancel":
        return messagebox.askokcancel(title_formatted, message_formatted)
    else:
        return messagebox.showinfo(title_formatted, message_formatted)

# عنصر تبويب للصفحات المختلفة
class RTLNotebook(ttb.Notebook):
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.settings = config.load_settings()

    def add_tab(self, frame, title):
        self.add(frame, text=format_arabic_text(title))
