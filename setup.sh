#!/bin/bash

echo "جاري فحص متطلبات برنامج إدارة مخزون المطحنة..."
echo "============================================="
echo

# البحث عن بايثون في مسارات مختلفة
PYTHON_CMD=""
PIP_CMD=""

# فحص بايثون في مسارات مختلفة
for python_path in python python3 /usr/bin/python3 /usr/local/bin/python3 /opt/python3/bin/python3; do
    if command -v "$python_path" &> /dev/null; then
        # التحقق من أن بايثون يعمل فعلاً
        if "$python_path" --version &> /dev/null; then
            PYTHON_CMD="$python_path"
            echo "تم العثور على بايثون: $PYTHON_CMD"
            break
        fi
    fi
done

if [ -z "$PYTHON_CMD" ]; then
    echo "❌ خطأ: لم يتم العثور على بايثون صالح للاستخدام!"
    echo
    echo "يرجى تثبيت بايثون 3.8 أو إصدار أحدث:"
    echo "• Windows: قم بتحميل بايثون من https://www.python.org/downloads/"
    echo "• Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
    echo "• CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "• Arch Linux: sudo pacman -S python python-pip"
    echo
    echo "تأكد من إضافة بايثون إلى متغير PATH"
    read -p "اضغط Enter للخروج..."
    exit 1
fi

# البحث عن pip
for pip_path in pip pip3 "$PYTHON_CMD -m pip"; do
    if eval "$pip_path --version" &> /dev/null; then
        PIP_CMD="$pip_path"
        echo "تم العثور على pip: $PIP_CMD"
        break
    fi
done

if [ -z "$PIP_CMD" ]; then
    echo "❌ خطأ: لم يتم العثور على pip!"
    echo "يرجى تثبيت pip أو استخدام: $PYTHON_CMD -m ensurepip --upgrade"
    read -p "اضغط Enter للخروج..."
    exit 1
fi

echo
echo "جاري تثبيت المكتبات المطلوبة..."
echo "================================"

if eval "$PIP_CMD install -r requirements.txt"; then
    echo
    echo "✅ تم تثبيت جميع المتطلبات بنجاح!"
else
    echo
    echo "❌ حدث خطأ أثناء تثبيت المكتبات!"
    echo "يرجى التحقق من اتصال الإنترنت ومحاولة تشغيل الأمر التالي يدوياً:"
    echo "$PIP_CMD install -r requirements.txt"
    read -p "اضغط Enter للخروج..."
    exit 1
fi

echo
echo "يمكنك الآن تشغيل البرنامج عن طريق تشغيل ملف main.py"
echo "أو استخدام الأمر: $PYTHON_CMD main.py"
echo
read -p "اضغط Enter لتشغيل البرنامج الآن..."

echo "جاري تشغيل البرنامج..."
eval "$PYTHON_CMD main.py"
