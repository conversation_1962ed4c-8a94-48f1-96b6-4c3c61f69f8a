
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import sys
from PIL import Image, ImageTk
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import arabic_reshaper
from bidi.algorithm import get_display
import time
from datetime import datetime

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, FinishedProductsManager

class FinishedProductsScreen(ttb.Frame):
    def __init__(self, parent, db_manager, products_manager=None):
        super().__init__(parent)

        self.settings = config.load_settings()

        # إنشاء مديري قاعدة البيانات
        self.db_manager = db_manager
        self.products_manager = products_manager or FinishedProductsManager(db_manager)

        # تخزين المنتج المحدد حالياً
        self.selected_product_id = None

        # إنشاء واجهة المستخدم
        self.create_widgets()

        # تحديث قائمة المنتجات الجاهزة
        self.update_products_list()

    def create_widgets(self):
        # العنوان الرئيسي
        header_frame = ttb.Frame(self)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 20))

        title = ui.RTLLabel(
            header_frame, 
            text="إدارة المنتجات الجاهزة", 
            font=("Tajawal", 24, "bold"),
            bootstyle="primary"
        )
        title.pack(anchor="e", pady=10)

        # إطار البحث
        search_frame = ttb.Frame(header_frame)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=10)

        search_box = ui.RTLSearchBox(
            search_frame, 
            search_callback=self.search_products,
            placeholder="بحث عن منتج جاهز..."
        )
        search_box.pack(fill=tk.X, expand=True)

        # إطار الأزرار
        button_frame = ttb.Frame(header_frame)
        button_frame.pack(side=tk.RIGHT, padx=10)

        # زر إضافة منتج جديد
        add_btn = ttb.Button(
            button_frame, 
            text=ui.format_arabic_text("إضافة منتج جديد"),
            command=self.add_product,
            bootstyle="success",
            width=15
        )
        add_btn.pack(side=tk.LEFT, padx=5)

        # زر طباعة تقرير
        print_btn = ttb.Button(
            button_frame, 
            text=ui.format_arabic_text("طباعة تقرير"),
            command=self.print_report,
            bootstyle="info",
            width=15
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # تقسيم الشاشة إلى قسمين
        # 1. قائمة المنتجات الجاهزة
        # 2. نموذج تحرير المنتج

        # إطار رئيسي للمحتوى
        content_frame = ttb.Frame(self)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # إطار قائمة المنتجات
        products_list_frame = ttb.Frame(content_frame)
        products_list_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # قائمة المنتجات الجاهزة
        self.create_products_list(products_list_frame)

        # إطار تفاصيل المنتج
        product_details_frame = ttb.Frame(content_frame)
        product_details_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # نموذج تحرير المنتج
        self.create_product_form(product_details_frame)

    def create_products_list(self, parent):
        # عنوان الجدول
        list_title = ui.RTLLabel(
            parent, 
            text="قائمة المنتجات الجاهزة", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        list_title.pack(anchor="e", pady=(0, 10))

        # إطار الجدول مع شريط التمرير
        table_frame = ttb.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # عمود الجدول
        columns = ("id", "code", "name", "weight", "quantity", "unit")
        headers = ("م", "الرمز", "الاسم", "الوزن", "الكمية", "الوحدة")

        # إنشاء جدول المنتجات
        self.products_table = ui.RTLTreeview(
            table_frame,
            columns=columns,
            headers=headers
        )
        self.products_table.pack(fill=tk.BOTH, expand=True)

        # عرض الأعمدة بشكل مناسب
        self.products_table.column("id", width=50)
        self.products_table.column("code", width=100)
        self.products_table.column("name", width=200)
        self.products_table.column("weight", width=100)
        self.products_table.column("quantity", width=100)
        self.products_table.column("unit", width=80)

        # ربط حدث النقر على العنصر
        self.products_table.bind("<<TreeviewSelect>>", self.on_product_selected)

    def create_product_form(self, parent):
        # عنوان النموذج
        form_title = ui.RTLLabel(
            parent, 
            text="بيانات المنتج الجاهز", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        form_title.pack(anchor="e", pady=(0, 20))

        # إطار النموذج
        form_frame = ttb.Frame(parent)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10)

        # حقول النموذج
        # رمز المنتج
        code_frame = ttb.Frame(form_frame)
        code_frame.pack(fill=tk.X, pady=5)

        code_label = ui.RTLLabel(code_frame, text="رمز المنتج:", width=15)
        code_label.pack(side=tk.RIGHT, padx=5)

        self.code_entry = ttb.Entry(code_frame)
        self.code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # اسم المنتج
        name_frame = ttb.Frame(form_frame)
        name_frame.pack(fill=tk.X, pady=5)

        name_label = ui.RTLLabel(name_frame, text="اسم المنتج:", width=15)
        name_label.pack(side=tk.RIGHT, padx=5)

        self.name_entry = ttb.Entry(name_frame)
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوزن
        weight_frame = ttb.Frame(form_frame)
        weight_frame.pack(fill=tk.X, pady=5)

        weight_label = ui.RTLLabel(weight_frame, text="الوزن:", width=15)
        weight_label.pack(side=tk.RIGHT, padx=5)

        self.weight_entry = ttb.Entry(weight_frame)
        self.weight_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الكمية
        quantity_frame = ttb.Frame(form_frame)
        quantity_frame.pack(fill=tk.X, pady=5)

        quantity_label = ui.RTLLabel(quantity_frame, text="الكمية:", width=15)
        quantity_label.pack(side=tk.RIGHT, padx=5)

        self.quantity_entry = ttb.Entry(quantity_frame)
        self.quantity_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوحدة
        unit_frame = ttb.Frame(form_frame)
        unit_frame.pack(fill=tk.X, pady=5)

        unit_label = ui.RTLLabel(unit_frame, text="الوحدة:", width=15)
        unit_label.pack(side=tk.RIGHT, padx=5)

        # قائمة الوحدات الشائعة
        self.unit_var = tk.StringVar()
        units = ["كجم", "جرام", "علبة", "كيس", "كرتون", "قطعة"]

        unit_combobox = ttb.Combobox(unit_frame, textvariable=self.unit_var, values=units)
        unit_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # الوصف
        desc_frame = ttb.Frame(form_frame)
        desc_frame.pack(fill=tk.X, pady=5)

        desc_label = ui.RTLLabel(desc_frame, text="الوصف:", width=15)
        desc_label.pack(side=tk.RIGHT, padx=5, anchor="n")

        self.desc_text = ttb.Text(desc_frame, height=4)
        self.desc_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # إطار أزرار العمليات
        actions_frame = ttb.Frame(parent)
        actions_frame.pack(fill=tk.X, pady=20)

        # زر الحفظ
        save_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("حفظ التغييرات"),
            command=self.save_product,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=tk.RIGHT, padx=5)

        # زر الحذف
        delete_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("حذف المنتج"),
            command=self.delete_product,
            bootstyle="danger",
            width=15
        )
        delete_btn.pack(side=tk.RIGHT, padx=5)

        # زر إلغاء
        cancel_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("إلغاء"),
            command=self.clear_form,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT, padx=5)

        # تعطيل الحقول في البداية
        self.disable_form()

    def update_products_list(self):
        # مسح الجدول
        self.products_table.clear_all()

        # الحصول على جميع المنتجات
        try:
            products = self.products_manager.get_all_products()
            if products:
                # إضافة المنتجات إلى الجدول
                for product in products:
                    self.products_table.add_row([
                        product.ProductID,
                        product.ProductCode,
                        product.ProductName,
                        product.Weight,
                        product.Quantity,
                        product.Unit or ""
                    ])
        except Exception as e:
            print(f"خطأ في تحديث قائمة المنتجات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء تحديث قائمة المنتجات: {str(e)}", "error")

    def search_products(self, search_term):
        # مسح الجدول
        self.products_table.clear_all()

        if not search_term:
            # إذا كان البحث فارغاً، عرض جميع المنتجات
            self.update_products_list()
            return

        # البحث عن المنتجات المطابقة
        try:
            products = self.products_manager.search_products(search_term)
            if products:
                # إضافة المنتجات إلى الجدول
                for product in products:
                    self.products_table.add_row([
                        product.ProductID,
                        product.ProductCode,
                        product.ProductName,
                        product.Weight,
                        product.Quantity,
                        product.Unit or ""
                    ])
        except Exception as e:
            print(f"خطأ في البحث عن المنتجات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء البحث عن المنتجات: {str(e)}", "error")

    def on_product_selected(self, event):
        # الحصول على العنصر المحدد
        selected_item = self.products_table.selection()
        if not selected_item:
            return

        item = self.products_table.item(selected_item[0])
        product_id = item["values"][0]

        # تحميل بيانات المنتج
        try:
            product = self.products_manager.get_product_by_id(product_id)
            if product:
                self.selected_product_id = product.ProductID
                self.enable_form()
                self.populate_form(product)
        except Exception as e:
            print(f"خطأ في تحميل بيانات المنتج: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء تحميل بيانات المنتج: {str(e)}", "error")

    def populate_form(self, product):
        # ملء حقول النموذج ببيانات المنتج
        self.code_entry.delete(0, tk.END)
        self.code_entry.insert(0, product.ProductCode)
        self.code_entry.configure(state="disabled")  # لا يمكن تغيير الرمز

        self.name_entry.delete(0, tk.END)
        self.name_entry.insert(0, product.ProductName)

        self.weight_entry.delete(0, tk.END)
        self.weight_entry.insert(0, str(product.Weight) if product.Weight else "")

        self.quantity_entry.delete(0, tk.END)
        self.quantity_entry.insert(0, str(product.Quantity) if product.Quantity else "")

        self.unit_var.set(product.Unit if product.Unit else "")

        self.desc_text.delete("1.0", tk.END)
        if product.Description:
            self.desc_text.insert("1.0", product.Description)

    def enable_form(self):
        self.code_entry.configure(state="normal")
        self.name_entry.configure(state="normal")
        self.weight_entry.configure(state="normal")
        self.quantity_entry.configure(state="normal")
        self.desc_text.configure(state="normal")

    def disable_form(self):
        self.clear_form()
        self.code_entry.configure(state="disabled")
        self.name_entry.configure(state="disabled")
        self.weight_entry.configure(state="disabled")
        self.quantity_entry.configure(state="disabled")
        self.desc_text.configure(state="disabled")

    def clear_form(self):
        self.selected_product_id = None
        self.code_entry.configure(state="normal")
        self.code_entry.delete(0, tk.END)
        self.name_entry.delete(0, tk.END)
        self.weight_entry.delete(0, tk.END)
        self.quantity_entry.delete(0, tk.END)
        self.unit_var.set("")
        self.desc_text.delete("1.0", tk.END)

    def add_product(self):
        # تفعيل النموذج وتنظيفه
        self.selected_product_id = None
        self.enable_form()
        self.clear_form()
        self.code_entry.focus_set()

    def save_product(self):
        # التحقق من صحة البيانات
        code = self.code_entry.get().strip()
        name = self.name_entry.get().strip()
        weight_str = self.weight_entry.get().strip()
        quantity_str = self.quantity_entry.get().strip()
        unit = self.unit_var.get()
        description = self.desc_text.get("1.0", "end-1c").strip()

        # التحقق من وجود الرمز والاسم
        if not code or not name:
            ui.arabic_messagebox("تنبيه", "يجب إدخال رمز واسم المنتج", "warning")
            return

        # تحويل الوزن والكمية إلى أرقام
        try:
            weight = float(weight_str) if weight_str else 0
            quantity = float(quantity_str) if quantity_str else 0
        except ValueError:
            ui.arabic_messagebox("خطأ", "يرجى إدخال قيم رقمية صحيحة للوزن والكمية", "error")
            return

        try:
            if self.selected_product_id:
                # تحديث منتج موجود
                success = self.products_manager.update_product(
                    self.selected_product_id, name, weight, quantity, unit, description
                )
                if success:
                    ui.arabic_messagebox("نجاح", "تم تحديث المنتج بنجاح", "info")
                    self.update_products_list()
                    self.clear_form()
                    self.disable_form()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في تحديث المنتج", "error")
            else:
                # التحقق من عدم تكرار الرمز
                existing_product = self.products_manager.get_product_by_code(code)
                if existing_product:
                    ui.arabic_messagebox("تنبيه", "رمز المنتج مستخدم بالفعل، يرجى استخدام رمز آخر", "warning")
                    return

                # إضافة منتج جديد
                success = self.products_manager.add_product(
                    code, name, weight, quantity, unit, description
                )
                if success:
                    ui.arabic_messagebox("نجاح", "تم إضافة المنتج بنجاح", "info")
                    self.update_products_list()
                    self.clear_form()
                    self.disable_form()
                else:
                    ui.arabic_messagebox("خطأ", "فشل في إضافة المنتج", "error")
        except Exception as e:
            print(f"خطأ في حفظ المنتج: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء حفظ المنتج: {str(e)}", "error")

    def delete_product(self):
        if not self.selected_product_id:
            ui.arabic_messagebox("تنبيه", "يرجى تحديد منتج للحذف", "warning")
            return

        # التأكيد قبل الحذف
        confirm = ui.arabic_messagebox(
            "تأكيد الحذف", 
            "هل أنت متأكد من رغبتك في حذف هذا المنتج؟",
            "yesno"
        )

        if confirm:
            try:
                success = self.products_manager.delete_product(self.selected_product_id)
                if success:
                    ui.arabic_messagebox("نجاح", "تم حذف المنتج بنجاح", "info")
                    self.update_products_list()
                    self.clear_form()
                    self.disable_form()
                else:
                    ui.arabic_messagebox(
                        "تنبيه", 
                        "لا يمكن حذف المنتج لأنه مرتبط بخلطات. يرجى إزالة هذه الارتباطات أولاً.",
                        "warning"
                    )
            except Exception as e:
                print(f"خطأ في حذف المنتج: {e}")
                ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء حذف المنتج: {str(e)}", "error")

    def print_report(self):
        # اختيار مكان حفظ التقرير
        file_path = filedialog.asksaveasfilename(
            title="حفظ تقرير المنتجات",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")]
        )

        if not file_path:
            return

        try:
            # إنشاء التقرير
            products = self.products_manager.get_all_products()

            if not products:
                ui.arabic_messagebox("تنبيه", "لا توجد منتجات لإنشاء التقرير", "warning")
                return

            # إنشاء ملف PDF
            doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)
            elements = []

            # الأنماط
            styles = getSampleStyleSheet()

            # العنوان
            title_style = styles['Heading1']
            title_style.alignment = 1  # وسط
            title = Paragraph("تقرير المنتجات الجاهزة", title_style)
            elements.append(title)
            elements.append(Spacer(1, 20))

            # التاريخ
            date_style = styles['Normal']
            date_style.alignment = 1
            date = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}", date_style)
            elements.append(date)
            elements.append(Spacer(1, 20))

            # بيانات الجدول
            data = [["الرمز", "الاسم", "الوزن", "الكمية", "الوحدة"]]

            for product in products:
                data.append([
                    product.ProductCode,
                    product.ProductName,
                    str(product.Weight) if product.Weight else "",
                    str(product.Quantity) if product.Quantity else "",
                    product.Unit or ""
                ])

            # إنشاء الجدول
            table = Table(data)

            # نمط الجدول
            table_style = TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ])

            table.setStyle(table_style)
            elements.append(table)

            # بناء المستند
            doc.build(elements)

            # فتح الملف بعد الإنشاء
            os.startfile(file_path)

        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}", "error")
