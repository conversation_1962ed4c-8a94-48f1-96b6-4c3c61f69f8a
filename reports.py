
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttb
from ttkbootstrap.constants import *
import os
import sys
import datetime
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
import arabic_reshaper
from bidi.algorithm import get_display

# إضافة المسار الحالي إلى مسارات النظام
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import config
import ui_components as ui
from database import DatabaseManager, RawMaterialsManager, FinishedProductsManager, MixturesManager

class ReportsScreen(ttb.Frame):
    def __init__(self, parent, db_manager, report_type="main"):
        super().__init__(parent)

        self.settings = config.load_settings()

        # إنشاء مديري قاعدة البيانات
        self.db_manager = db_manager
        self.raw_materials_manager = RawMaterialsManager(db_manager)
        self.products_manager = FinishedProductsManager(db_manager)
        self.mixtures_manager = MixturesManager(db_manager)

        # نوع التقرير الافتراضي
        self.report_type = report_type

        # إنشاء واجهة المستخدم
        self.create_widgets()

        # عرض التقرير المناسب
        self.show_report(report_type)

    def create_widgets(self):
        # العنوان الرئيسي
        header_frame = ttb.Frame(self)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 20))

        title = ui.RTLLabel(
            header_frame, 
            text="تقارير النظام", 
            font=("Tajawal", 24, "bold"),
            bootstyle="primary"
        )
        title.pack(anchor="e", pady=10)

        # أنواع التقارير
        report_types_frame = ttb.Frame(header_frame)
        report_types_frame.pack(fill=tk.X, pady=10)

        # أزرار أنواع التقارير
        raw_materials_btn = ttb.Button(
            report_types_frame, 
            text=ui.format_arabic_text("تقارير المواد الخام"),
            command=lambda: self.show_report("raw_materials"),
            bootstyle="outline",
            width=15
        )
        raw_materials_btn.pack(side=tk.RIGHT, padx=5)

        products_btn = ttb.Button(
            report_types_frame, 
            text=ui.format_arabic_text("تقارير المنتجات الجاهزة"),
            command=lambda: self.show_report("products"),
            bootstyle="outline",
            width=18
        )
        products_btn.pack(side=tk.RIGHT, padx=5)

        mixtures_btn = ttb.Button(
            report_types_frame, 
            text=ui.format_arabic_text("تقارير الخلطات"),
            command=lambda: self.show_report("mixtures"),
            bootstyle="outline",
            width=15
        )
        mixtures_btn.pack(side=tk.RIGHT, padx=5)

        # إطار محتوى التقرير
        self.report_content_frame = ttb.Frame(self)
        self.report_content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    def clear_report_content(self):
        # مسح جميع المكونات من إطار محتوى التقرير
        for widget in self.report_content_frame.winfo_children():
            widget.destroy()

    def show_report(self, report_type):
        self.report_type = report_type
        self.clear_report_content()

        if report_type == "raw_materials":
            self.show_raw_materials_report()
        elif report_type == "products":
            self.show_products_report()
        elif report_type == "mixtures":
            self.show_mixtures_report()
        else:
            self.show_main_report()

    def show_main_report(self):
        # عنوان
        title = ui.RTLLabel(
            self.report_content_frame, 
            text="التقارير المتاحة", 
            font=("Tajawal", 18, "bold"),
            bootstyle="secondary"
        )
        title.pack(anchor="e", pady=20)

        # إطار للتقارير المتاحة
        reports_frame = ttb.Frame(self.report_content_frame)
        reports_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # قائمة التقارير
        reports = [
            ("تقرير المواد الخام", "raw_materials", "عرض قائمة المواد الخام مع الكميات والأوزان"),
            ("تقرير المنتجات الجاهزة", "products", "عرض قائمة المنتجات الجاهزة مع الكميات"),
            ("تقرير الخلطات", "mixtures", "عرض قائمة الخلطات ومكوناتها")
        ]

        for i, (name, type_code, desc) in enumerate(reports):
            report_card = ttb.Frame(reports_frame, bootstyle="light")
            report_card.pack(fill=tk.X, pady=10)

            report_name = ui.RTLLabel(
                report_card, 
                text=name, 
                font=("Tajawal", 14, "bold"),
                bootstyle="primary"
            )
            report_name.pack(anchor="e", padx=10, pady=(10, 5))

            report_desc = ui.RTLLabel(
                report_card, 
                text=desc,
                bootstyle="secondary"
            )
            report_desc.pack(anchor="e", padx=10, pady=(0, 5))

            view_btn = ttb.Button(
                report_card, 
                text=ui.format_arabic_text("عرض التقرير"),
                command=lambda t=type_code: self.show_report(t),
                bootstyle="info",
                width=12
            )
            view_btn.pack(side=tk.RIGHT, padx=10, pady=10)

            print_btn = ttb.Button(
                report_card, 
                text=ui.format_arabic_text("طباعة PDF"),
                command=lambda t=type_code: self.generate_pdf_report(t),
                bootstyle="success",
                width=12
            )
            print_btn.pack(side=tk.RIGHT, padx=10, pady=10)

    def show_raw_materials_report(self):
        # عنوان
        title = ui.RTLLabel(
            self.report_content_frame, 
            text="تقرير المواد الخام", 
            font=("Tajawal", 18, "bold"),
            bootstyle="secondary"
        )
        title.pack(anchor="e", pady=10)

        # أزرار العمليات
        actions_frame = ttb.Frame(self.report_content_frame)
        actions_frame.pack(fill=tk.X, pady=10)

        print_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("طباعة التقرير"),
            command=lambda: self.generate_pdf_report("raw_materials"),
            bootstyle="success",
            width=15
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # إطار جدول المواد الخام
        table_frame = ttb.Frame(self.report_content_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # إنشاء جدول المواد
        columns = ("id", "code", "name", "weight", "quantity", "unit", "last_updated")
        headers = ("م", "الرمز", "الاسم", "الوزن", "الكمية", "الوحدة", "آخر تحديث")

        materials_table = ui.RTLTreeview(
            table_frame,
            columns=columns,
            headers=headers
        )
        materials_table.pack(fill=tk.BOTH, expand=True)

        # تنسيق الأعمدة
        materials_table.column("id", width=50)
        materials_table.column("code", width=100)
        materials_table.column("name", width=200)
        materials_table.column("weight", width=80)
        materials_table.column("quantity", width=80)
        materials_table.column("unit", width=80)
        materials_table.column("last_updated", width=120)

        # استرجاع بيانات المواد الخام
        try:
            materials = self.raw_materials_manager.get_all_materials()
            if materials:
                for material in materials:
                    last_updated = material.LastUpdated.strftime("%Y-%m-%d") if material.LastUpdated else ""
                    materials_table.add_row([
                        material.MaterialID,
                        material.MaterialCode,
                        material.MaterialName,
                        material.Weight,
                        material.Quantity,
                        material.Unit or "",
                        last_updated
                    ])
        except Exception as e:
            print(f"خطأ في استرجاع بيانات المواد الخام: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء استرجاع بيانات المواد الخام: {str(e)}", "error")

    def show_products_report(self):
        # عنوان
        title = ui.RTLLabel(
            self.report_content_frame, 
            text="تقرير المنتجات الجاهزة", 
            font=("Tajawal", 18, "bold"),
            bootstyle="secondary"
        )
        title.pack(anchor="e", pady=10)

        # أزرار العمليات
        actions_frame = ttb.Frame(self.report_content_frame)
        actions_frame.pack(fill=tk.X, pady=10)

        print_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("طباعة التقرير"),
            command=lambda: self.generate_pdf_report("products"),
            bootstyle="success",
            width=15
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # إطار جدول المنتجات
        table_frame = ttb.Frame(self.report_content_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # إنشاء جدول المنتجات
        columns = ("id", "code", "name", "weight", "quantity", "unit", "last_updated")
        headers = ("م", "الرمز", "الاسم", "الوزن", "الكمية", "الوحدة", "آخر تحديث")

        products_table = ui.RTLTreeview(
            table_frame,
            columns=columns,
            headers=headers
        )
        products_table.pack(fill=tk.BOTH, expand=True)

        # تنسيق الأعمدة
        products_table.column("id", width=50)
        products_table.column("code", width=100)
        products_table.column("name", width=200)
        products_table.column("weight", width=80)
        products_table.column("quantity", width=80)
        products_table.column("unit", width=80)
        products_table.column("last_updated", width=120)

        # استرجاع بيانات المنتجات
        try:
            products = self.products_manager.get_all_products()
            if products:
                for product in products:
                    last_updated = product.LastUpdated.strftime("%Y-%m-%d") if product.LastUpdated else ""
                    products_table.add_row([
                        product.ProductID,
                        product.ProductCode,
                        product.ProductName,
                        product.Weight,
                        product.Quantity,
                        product.Unit or "",
                        last_updated
                    ])
        except Exception as e:
            print(f"خطأ في استرجاع بيانات المنتجات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء استرجاع بيانات المنتجات: {str(e)}", "error")

    def show_mixtures_report(self):
        # عنوان
        title = ui.RTLLabel(
            self.report_content_frame, 
            text="تقرير الخلطات", 
            font=("Tajawal", 18, "bold"),
            bootstyle="secondary"
        )
        title.pack(anchor="e", pady=10)

        # أزرار العمليات
        actions_frame = ttb.Frame(self.report_content_frame)
        actions_frame.pack(fill=tk.X, pady=10)

        print_btn = ttb.Button(
            actions_frame, 
            text=ui.format_arabic_text("طباعة التقرير"),
            command=lambda: self.generate_pdf_report("mixtures"),
            bootstyle="success",
            width=15
        )
        print_btn.pack(side=tk.LEFT, padx=5)

        # إطار محتوى التقرير
        mixtures_content = ttb.Frame(self.report_content_frame)
        mixtures_content.pack(fill=tk.BOTH, expand=True, pady=10)

        # إطار قائمة الخلطات
        mixtures_list_frame = ttb.Frame(mixtures_content)
        mixtures_list_frame.pack(side=tk.TOP, fill=tk.X, pady=10)

        # عنوان قائمة الخلطات
        list_title = ui.RTLLabel(
            mixtures_list_frame, 
            text="قائمة الخلطات", 
            font=("Tajawal", 14, "bold"),
            bootstyle="primary"
        )
        list_title.pack(anchor="e", pady=(0, 10))

        # جدول الخلطات
        columns = ("id", "code", "name", "op_code", "creation_date")
        headers = ("م", "الرمز", "الاسم", "الكود التشغيلي", "تاريخ الإنشاء")

        mixtures_table = ui.RTLTreeview(
            mixtures_list_frame,
            columns=columns,
            headers=headers
        )
        mixtures_table.pack(fill=tk.X, expand=True)

        # تنسيق الأعمدة
        mixtures_table.column("id", width=50)
        mixtures_table.column("code", width=100)
        mixtures_table.column("name", width=200)
        mixtures_table.column("op_code", width=100)
        mixtures_table.column("creation_date", width=120)

        # إطار تفاصيل المكونات
        components_frame = ttb.Labelframe(mixtures_content, text=ui.format_arabic_text("مكونات الخلطة المحددة"))
        components_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # جدول المكونات
        columns = ("material_code", "material_name", "quantity", "unit")
        headers = ("كود المادة", "اسم المادة", "الكمية", "الوحدة")

        components_table = ui.RTLTreeview(
            components_frame,
            columns=columns,
            headers=headers
        )
        components_table.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تنسيق الأعمدة
        components_table.column("material_code", width=100)
        components_table.column("material_name", width=200)
        components_table.column("quantity", width=80)
        components_table.column("unit", width=80)

        # استرجاع بيانات الخلطات
        try:
            mixtures = self.mixtures_manager.get_all_mixtures()
            if mixtures:
                for mixture in mixtures:
                    creation_date = mixture.CreationDate.strftime("%Y-%m-%d") if mixture.CreationDate else ""
                    mixtures_table.add_row([
                        mixture.MixtureID,
                        mixture.MixtureCode,
                        mixture.MixtureName,
                        mixture.OperationalCode,
                        creation_date
                    ])

                # عند اختيار خلطة، عرض مكوناتها
                def on_mixture_selected(event):
                    selected_item = mixtures_table.selection()
                    if not selected_item:
                        return

                    item = mixtures_table.item(selected_item[0])
                    mixture_id = item["values"][0]

                    # مسح جدول المكونات
                    for row in components_table.get_children():
                        components_table.delete(row)

                    # استرجاع مكونات الخلطة
                    components = self.mixtures_manager.get_mixture_components(mixture_id)
                    if components:
                        for comp in components:
                            components_table.add_row([
                                comp.MaterialCode,
                                comp.MaterialName,
                                comp.Quantity,
                                comp.Unit or ""
                            ])

                mixtures_table.bind("<<TreeviewSelect>>", on_mixture_selected)
        except Exception as e:
            print(f"خطأ في استرجاع بيانات الخلطات: {e}")
            ui.arabic_messagebox("خطأ", f"حدث خطأ أثناء استرجاع بيانات الخلطات: {str(e)}", "error")

    def generate_pdf_report(self, report_type):
        try:
            # تحديد مكان حفظ الملف
            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ التقرير كملف PDF"
            )

            if not file_path:
                return  # المستخدم ألغى العملية

            # إنشاء مستند PDF
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )

            # تهيئة قائمة العناصر
            elements = []

            # إنشاء الأنماط
            styles = getSampleStyleSheet()

            # إضافة نمط عربي (سوف نحتاج إلى تسجيل خط عربي للاستخدام الكامل)
            arabic_style = ParagraphStyle(
                'arabic',
                parent=styles['Heading1'],
                alignment=2,  # يمين إلى يسار
                fontName='Helvetica',
                fontSize=16,
                spaceAfter=12
            )

            arabic_normal = ParagraphStyle(
                'arabic_normal',
                parent=styles['Normal'],
                alignment=2,  # يمين إلى يسار
                fontName='Helvetica',
                fontSize=10,
                spaceAfter=6
            )

            # إضافة عنوان التقرير
            if report_type == "raw_materials":
                title_text = "تقرير المواد الخام"
                elements.append(Paragraph(arabic_reshaper.reshape(title_text), arabic_style))

                # جلب بيانات المواد الخام
                materials = self.raw_materials_manager.get_all_materials()
                if materials:
                    # إنشاء جدول البيانات
                    data = [["م", "الرمز", "الاسم", "الوزن", "الكمية", "الوحدة"]]
                    for i, material in enumerate(materials, 1):
                        data.append([
                            str(i),
                            material.MaterialCode,
                            arabic_reshaper.reshape(material.MaterialName),
                            str(material.Weight),
                            str(material.Quantity),
                            arabic_reshaper.reshape(material.Unit or "")
                        ])

                    # إنشاء جدول PDF
                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    elements.append(table)

            elif report_type == "products":
                title_text = "تقرير المنتجات الجاهزة"
                elements.append(Paragraph(arabic_reshaper.reshape(title_text), arabic_style))

                # جلب بيانات المنتجات
                products = self.products_manager.get_all_products()
                if products:
                    # إنشاء جدول البيانات
                    data = [["م", "الرمز", "الاسم", "الوزن", "الكمية", "الوحدة"]]
                    for i, product in enumerate(products, 1):
                        data.append([
                            str(i),
                            product.ProductCode,
                            arabic_reshaper.reshape(product.ProductName),
                            str(product.Weight),
                            str(product.Quantity),
                            arabic_reshaper.reshape(product.Unit or "")
                        ])

                    # إنشاء جدول PDF
                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    elements.append(table)

            elif report_type == "mixtures":
                title_text = "تقرير الخلطات"
                elements.append(Paragraph(arabic_reshaper.reshape(title_text), arabic_style))

                # جلب بيانات الخلطات
                mixtures = self.mixtures_manager.get_all_mixtures()
                if mixtures:
                    # إنشاء جدول البيانات للخلطات
                    data = [["م", "الرمز", "الاسم", "الكود التشغيلي"]]
                    for i, mixture in enumerate(mixtures, 1):
                        data.append([
                            str(i),
                            mixture.MixtureCode,
                            arabic_reshaper.reshape(mixture.MixtureName),
                            mixture.OperationalCode
                        ])

                    # إنشاء جدول PDF
                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))

                    elements.append(table)
                    elements.append(Spacer(1, 20))

                    # إضافة تفاصيل لكل خلطة
                    for mixture in mixtures:
                        mixture_title = f"مكونات خلطة: {mixture.MixtureName} ({mixture.MixtureCode})"
                        elements.append(Paragraph(arabic_reshaper.reshape(mixture_title), arabic_normal))

                        components = self.mixtures_manager.get_mixture_components(mixture.MixtureID)
                        if components:
                            comp_data = [["اسم المادة", "الكمية", "الوحدة"]]
                            for comp in components:
                                comp_data.append([
                                    arabic_reshaper.reshape(comp.MaterialName),
                                    str(comp.Quantity),
                                    arabic_reshaper.reshape(comp.Unit or "")
                                ])

                            comp_table = Table(comp_data)
                            comp_table.setStyle(TableStyle([
                                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                                ('GRID', (0, 0), (-1, -1), 1, colors.black)
                            ]))

                            elements.append(comp_table)
                            elements.append(Spacer(1, 20))

            # بناء المستند
            doc.build(elements)

            # إظهار رسالة نجاح
            ui.arabic_messagebox(
                "نجاح", 
                f"تم حفظ التقرير بنجاح في المسار: {file_path}", 
                "info"
            )

            # فتح الملف بعد إنشائه
            os.startfile(file_path)

        except Exception as e:
            print(f"خطأ في إنشاء ملف PDF: {e}")
            ui.arabic_messagebox(
                "خطأ", 
                f"حدث خطأ أثناء إنشاء ملف PDF: {str(e)}", 
                "error"
            )

# استخدام الفئة
if __name__ == "__main__":
    root = ttb.Window(themename="pulse")
    db_manager = DatabaseManager()
    reports_screen = ReportsScreen(root, db_manager)
    reports_screen.pack(fill=tk.BOTH, expand=True)
    root.mainloop()
