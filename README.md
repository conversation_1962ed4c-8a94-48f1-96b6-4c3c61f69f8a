# مركز المخزون

نظام متكامل لإدارة المخزون في المطاحن باللغة العربية، يتيح للمستخدمين إدارة المواد الخام والمنتجات النهائية والخلطات مع تقارير شاملة.

![شعار مركز المخزون](assets/logo.png)

## المميزات الرئيسية

1. إدارة المستخدمين وصلاحيات الوصول
2. إدارة المواد الخام (إضافة، تعديل، حذف، بحث)
3. إدارة المنتجات الجاهزة
4. إدارة الخلطات والمطاحين
5. تقارير شاملة مع إمكانية الطباعة وتصدير PDF
6. واجهة مستخدم عربية سهلة الاستخدام

## متطلبات النظام

- نظام التشغيل: Windows 10 أو أحدث
- Python 3.8 أو أحدث
- قاعدة بيانات Microsoft Access

## طريقة التثبيت

### الخطوة الأولى: تثبيت Python
1. قم بتحميل Python 3.8 أو أحدث من [الموقع الرسمي](https://www.python.org/downloads/)
2. أثناء التثبيت، تأكد من تحديد خيار **"Add Python to PATH"**
3. تأكد من التثبيت بفتح موجه الأوامر وكتابة: `python --version`

### الخطوة الثانية: تشغيل ملف الإعداد
**لنظام Windows:**
```bash
setup.bat
```

**لأنظمة Linux/Mac:**
```bash
chmod +x setup.sh
./setup.sh
```

### الخطوة الثالثة: تشغيل البرنامج
```bash
python main.py
```

### بيانات تسجيل الدخول الافتراضية
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## حل المشاكل الشائعة

### مشكلة: "Python was not found"
**الحل:**
1. تأكد من تثبيت Python بشكل صحيح
2. أعد تشغيل موجه الأوامر بعد تثبيت Python
3. تأكد من إضافة Python إلى متغير PATH

### مشكلة: "pip is not recognized"
**الحل:**
```bash
python -m ensurepip --upgrade
python -m pip install -r requirements.txt
```

### مشكلة: فشل تثبيت المكتبات
**الحل:**
```bash
python -m pip install --upgrade pip
python -m pip install -r requirements.txt --user
```

## الاتصال والدعم

للحصول على الدعم الفني أو استفسارات إضافية، يرجى التواصل معنا.

